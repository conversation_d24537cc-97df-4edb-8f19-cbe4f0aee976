/*
 * 测试程序 - 验证payload反编译代码的正确性
 * 编译: gcc -o test_payload test_payload.c -lcurl -lssl -lcrypto
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>

// 模拟配置数据结构
typedef struct {
    int type;
    union {
        short s_value;
        int i_value;
        void *p_value;
    } value;
} setting;

// 模拟全局变量
static setting test_settings[100];

// 模拟配置访问函数
short setting_short(int id) {
    if (test_settings[id].type == 1) {
        return test_settings[id].value.s_value;
    }
    return 0;
}

int setting_int(int id) {
    if (test_settings[id].type == 2) {
        return test_settings[id].value.i_value;
    }
    return 0;
}

char *setting_ptr(int id) {
    if (test_settings[id].type == 3) {
        return (char *)test_settings[id].value.p_value;
    }
    return NULL;
}

// 初始化测试配置
void init_test_config() {
    // 字段1: 未知配置
    test_settings[1].type = 1;
    test_settings[1].value.s_value = 0;
    
    // 字段2: 端口号 8080
    test_settings[2].type = 1;
    test_settings[2].value.s_value = 8080;
    
    // 字段3: 心跳间隔 38500ms
    test_settings[3].type = 2;
    test_settings[3].value.i_value = 38500;
    
    // 字段4: 缓冲区大小
    test_settings[4].type = 2;
    test_settings[4].value.i_value = 1398104;
    
    // 字段5: Jitter值 27
    test_settings[5].type = 1;
    test_settings[5].value.s_value = 27;
    
    // 字段8: 主机列表
    test_settings[8].type = 3;
    test_settings[8].value.p_value = strdup("***************,backup.example.com");
    
    // 字段9: User-Agent
    test_settings[9].type = 3;
    test_settings[9].value.p_value = strdup("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
    
    // 字段10: 提交URL
    test_settings[10].type = 3;
    test_settings[10].value.p_value = strdup("/submit.php");
}

// 测试配置解析
void test_config_parsing() {
    printf("=== 配置解析测试 ===\n");
    
    printf("端口号: %d\n", setting_short(2));
    printf("心跳间隔: %d ms (%.1f 秒)\n", setting_int(3), setting_int(3) / 1000.0);
    printf("缓冲区大小: %d bytes\n", setting_int(4));
    printf("Jitter值: %d\n", setting_short(5));
    printf("主机列表: %s\n", setting_ptr(8));
    printf("User-Agent: %s\n", setting_ptr(9));
    printf("提交URL: %s\n", setting_ptr(10));
    
    // 验证关键值
    if (setting_short(2) == 8080) {
        printf("✓ 端口号解析正确\n");
    } else {
        printf("✗ 端口号解析错误\n");
    }
    
    if (setting_int(3) == 38500) {
        printf("✓ 心跳间隔解析正确\n");
    } else {
        printf("✗ 心跳间隔解析错误\n");
    }
    
    if (setting_short(5) == 27) {
        printf("✓ Jitter值解析正确\n");
    } else {
        printf("✗ Jitter值解析错误\n");
    }
    
    char *hosts = setting_ptr(8);
    if (hosts && strstr(hosts, "***************")) {
        printf("✓ 目标IP地址存在于主机列表中\n");
    } else {
        printf("✗ 目标IP地址未找到\n");
    }
}

// 测试XOR解密
void test_xor_decryption() {
    printf("\n=== XOR解密测试 ===\n");
    
    // 模拟加密数据（前16字节）
    unsigned char encrypted_data[] = {
        0x2e, 0x2f, 0x2e, 0x2f, 0x2e, 0x2c, 0x2e, 0x2e,
        0x2e, 0x2c, 0x2e, 0x2f, 0x2e, 0x2c, 0x31, 0xbe
    };
    
    unsigned char xor_key = 0x2e;
    
    printf("原始加密数据: ");
    for (int i = 0; i < 16; i++) {
        printf("%02x ", encrypted_data[i]);
    }
    printf("\n");
    
    // XOR解密
    for (int i = 0; i < 16; i++) {
        encrypted_data[i] ^= xor_key;
    }
    
    printf("解密后数据: ");
    for (int i = 0; i < 16; i++) {
        printf("%02x ", encrypted_data[i]);
    }
    printf("\n");
    
    // 解析前8字节作为两个字段
    uint16_t field1_id = (encrypted_data[0] << 8) | encrypted_data[1];
    uint16_t field1_type = (encrypted_data[2] << 8) | encrypted_data[3];
    uint16_t field1_length = (encrypted_data[4] << 8) | encrypted_data[5];
    uint16_t field1_value = (encrypted_data[6] << 8) | encrypted_data[7];
    
    printf("字段1: ID=%d, 类型=%d, 长度=%d, 值=%d\n", 
           field1_id, field1_type, field1_length, field1_value);
    
    uint16_t field2_id = (encrypted_data[8] << 8) | encrypted_data[9];
    uint16_t field2_type = (encrypted_data[10] << 8) | encrypted_data[11];
    uint16_t field2_length = (encrypted_data[12] << 8) | encrypted_data[13];
    uint16_t field2_value = (encrypted_data[14] << 8) | encrypted_data[15];
    
    printf("字段2: ID=%d, 类型=%d, 长度=%d, 值=%d\n", 
           field2_id, field2_type, field2_length, field2_value);
    
    if (field2_id == 2 && field2_value == 8080) {
        printf("✓ 端口8080解密验证成功\n");
    } else {
        printf("✗ 端口解密验证失败\n");
    }
}

// 测试网络连接逻辑
void test_network_logic() {
    printf("\n=== 网络连接逻辑测试 ===\n");
    
    char *host = "***************";
    int port = 8080;
    
    printf("目标主机: %s\n", host);
    printf("目标端口: %d\n", port);
    printf("连接URL: http://%s:%d/\n", host, port);
    
    // 模拟心跳计算
    int sleep_interval = 38500;  // 毫秒
    int jitter = 27;
    
    printf("基础心跳间隔: %d ms\n", sleep_interval);
    printf("Jitter值: %d%%\n", jitter);
    
    if (jitter > 0 && sleep_interval * jitter > 99) {
        int jitter_range = (sleep_interval * jitter) / 100;
        int random_offset = rand() % jitter_range;
        int actual_sleep = sleep_interval - random_offset;
        
        printf("Jitter范围: ±%d ms\n", jitter_range);
        printf("随机偏移: -%d ms\n", random_offset);
        printf("实际心跳间隔: %d ms (%.1f 秒)\n", actual_sleep, actual_sleep / 1000.0);
    }
}

// 测试命令类型
void test_command_types() {
    printf("\n=== 命令类型测试 ===\n");
    
    struct {
        unsigned int type;
        const char *name;
        const char *description;
    } commands[] = {
        {3, "command_die", "终止后门程序"},
        {4, "command_sleep", "调整心跳间隔"},
        {0x20, "command_ps_list", "获取进程列表"},
        {0x35, "command_file_list", "列出文件目录"},
        {0x36, "command_file_mkdir", "创建目录"},
        {0x37, "command_file_drives", "获取驱动器列表"},
        {0x38, "command_file_delete", "删除文件"},
        {78, "command_execjob", "执行系统命令"}
    };
    
    printf("支持的命令类型:\n");
    for (int i = 0; i < sizeof(commands) / sizeof(commands[0]); i++) {
        printf("  0x%02X (%3d): %-20s - %s\n", 
               commands[i].type, commands[i].type, 
               commands[i].name, commands[i].description);
    }
}

int main() {
    printf("Payload 后门程序反编译代码测试\n");
    printf("=====================================\n");
    
    // 初始化测试环境
    init_test_config();
    
    // 运行测试
    test_config_parsing();
    test_xor_decryption();
    test_network_logic();
    test_command_types();
    
    printf("\n=== 威胁分析总结 ===\n");
    printf("1. 确认目标: ***************:8080\n");
    printf("2. 心跳频率: 每38.5秒连接一次\n");
    printf("3. 通信协议: HTTP (伪装正常流量)\n");
    printf("4. 加密机制: RSA + AES + XOR\n");
    printf("5. 恶意功能: 文件操作、命令执行、进程管理\n");
    printf("6. 威胁等级: 🔴 高危后门程序\n");
    
    // 清理
    for (int i = 0; i < 100; i++) {
        if (test_settings[i].type == 3 && test_settings[i].value.p_value) {
            free(test_settings[i].value.p_value);
        }
    }
    
    return 0;
}
